"""
Azure transcript provider implementation using the standard OpenAI library.
"""

import asyncio
import hashlib
import json
import os
import tempfile
from typing import Dict, Optional, Union
from pydub import AudioSegment

from acva_ai._params import (
    AZURE_OPENAI_TRANSCRIPTION_ENDPOINT,
    OPENAI_MAX_AUDIO_SIZE,
    AZURE_OPENAI_TRANSCRIPTION_API_KEY,
    OPENAI_TRANSCRIPTION_MODEL_ID,
    AZURE_TRANSCRIPTION_API_VERSION,
)


# Define exceptions locally to avoid circular imports
class AudioTranscriptionError(Exception):
    """Custom exception for audio transcription errors."""

    pass


class AudioFileError(Exception):
    """Custom exception for audio file related errors."""

    pass


from acva_ai.llm.llm_cache import LLM_AUDIO_CACHE_DIR
from acva_ai.utils.usage import ResponseUsage
from acva_ai.utils.general_utils import calculate_cost
from acva_ai.utils.usage import LLMUsage
from acva_ai.audio_processing.audio_splitting import (
    merge_audio_chunks_by_duration,
    split_audio_segment_by_silence,
)

# Import OpenAI library
try:
    from openai import AzureOpenAI
except ImportError:
    raise ImportError(
        "OpenAI library is required for Azure transcript provider. Install with: pip install openai"
    )


class AzureTranscriptProvider:
    """
    Azure transcript provider implementation using the standard OpenAI library.

    This provider uses Azure OpenAI's Whisper API for audio transcription.
    It supports various audio formats and provides multiple response formats.
    """

    def __init__(self):
        """Initialize the Azure transcript provider."""
        self.provider_name = "azure"

        # Initialize Azure OpenAI client
        if not AZURE_OPENAI_TRANSCRIPTION_API_KEY:
            raise AudioTranscriptionError(
                "AZURE_OPENAI_API_KEY environment variable is not set"
            )

        if not AZURE_OPENAI_TRANSCRIPTION_ENDPOINT:
            raise AudioTranscriptionError(
                "AZURE_OPENAI_TRANSCRIPTION_ENDPOINT environment variable is not set"
            )

        self.client = AzureOpenAI(
            api_key=AZURE_OPENAI_TRANSCRIPTION_API_KEY,
            api_version=AZURE_TRANSCRIPTION_API_VERSION,
            azure_endpoint=AZURE_OPENAI_TRANSCRIPTION_ENDPOINT,
        )
        self.model_id = OPENAI_TRANSCRIPTION_MODEL_ID

    def get_max_file_size_mb(self) -> float:
        """Get the maximum file size supported by Azure."""
        return OPENAI_MAX_AUDIO_SIZE

    def get_provider_name(self) -> str:
        """Get the provider name."""
        return self.provider_name

    def _validate_file_size(self, file_path: str, max_size_mb: float) -> None:
        """Validate that the file is within the size limit."""
        try:
            size_mb = os.path.getsize(file_path) / (1024 * 1024)
            if size_mb > max_size_mb:
                raise AudioFileError(
                    f"Audio file too large: {size_mb:.2f} MB exceeds limit of {max_size_mb} MB"
                )
        except OSError as e:
            raise AudioFileError(f"Error checking file size: {e}")

    def _validate_audio_file(self, audio_file_path: str) -> bytes:
        """Validate and read audio file."""
        try:
            if not os.path.exists(audio_file_path):
                raise AudioFileError(f"Audio file not found: {audio_file_path}")

            if not os.path.isfile(audio_file_path):
                raise AudioFileError(f"Path is not a file: {audio_file_path}")

            with open(audio_file_path, "rb") as f:
                content = f.read()
                if not content:
                    raise AudioFileError(f"Audio file is empty: {audio_file_path}")
                return content

        except PermissionError:
            raise AudioFileError(f"Permission denied reading file: {audio_file_path}")
        except Exception as e:
            if isinstance(e, AudioFileError):
                raise
            raise AudioFileError(f"Error reading audio file: {e}")

    def _get_audio_duration_from_file(self, audio_file_path: str) -> float:
        """Get audio duration in seconds from file path."""
        try:
            audio = AudioSegment.from_file(audio_file_path)
            return len(audio) / 1000  # pydub duration is in milliseconds
        except Exception as e:
            raise AudioFileError(f"Error getting duration from file: {e}")

    def _get_audio_duration_from_segment(self, audio_segment: AudioSegment) -> float:
        """Get audio duration in seconds from AudioSegment."""
        return len(audio_segment) / 1000  # pydub duration is in milliseconds

    def _load_cached_response(self, cache_filepath: str) -> Optional[Dict]:
        """Load cached response if available."""
        try:
            if os.path.isfile(cache_filepath):
                with open(cache_filepath, "r", encoding="utf-8") as f:
                    return json.load(f)
        except (json.JSONDecodeError, IOError, OSError) as e:
            self.logger.warning(f"Error reading cache file: {e}")
        return None

    def _save_to_cache(self, cache_filepath: str, response_data: Dict) -> None:
        """Save response to cache."""
        try:
            os.makedirs(os.path.dirname(cache_filepath), exist_ok=True)
            with open(cache_filepath, "w", encoding="utf-8") as f:
                json.dump(response_data, f, ensure_ascii=False, indent=2)
        except (IOError, OSError) as e:
            self.logger.warning(f"Error writing to cache file: {e}")

    async def _calculate_usage_cost_from_file(
        self,
        audio_file_path: str,
        response_data: Dict,
        response_usage: ResponseUsage,
    ) -> None:
        """Calculate and track usage costs for file-based transcription."""
        try:
            audio_duration_seconds = self._get_audio_duration_from_file(audio_file_path)
            cost = await calculate_cost(
                model=self.model_id, audio_duration_seconds=audio_duration_seconds
            )

            llm_usage = LLMUsage(
                model_id=self.model_id,
                cost=cost,
                input_tokens=0,  # Not applicable for audio
                output_tokens=len(
                    response_data.get("text", "")
                ),  # Use text length as proxy
                audio_input_duration=audio_duration_seconds,
            )

            response_usage.add_llm_usage(llm_usage)
        except Exception as e:
            self.logger.warning(f"Error calculating transcription cost: {e}")

    async def _calculate_usage_cost_from_segment(
        self,
        audio_segment: AudioSegment,
        response_data: Dict,
        response_usage: ResponseUsage,
    ) -> None:
        """Calculate and track usage costs for AudioSegment-based transcription."""
        try:
            audio_duration_seconds = self._get_audio_duration_from_segment(
                audio_segment
            )
            cost = await calculate_cost(
                model=self.model_id, audio_duration_seconds=audio_duration_seconds
            )

            llm_usage = LLMUsage(
                model_id=self.model_id,
                cost=cost,
                input_tokens=0,  # Not applicable for audio
                output_tokens=len(
                    response_data.get("text", "")
                ),  # Use text length as proxy
                audio_input_duration=audio_duration_seconds,
            )

            response_usage.add_llm_usage(llm_usage)
        except Exception as e:
            self.logger.warning(f"Error calculating transcription cost: {e}")

    async def transcribe_audio_file(
        self,
        audio_file_path: str,
        language: Optional[str] = None,
        use_cache: bool = True,
        response_usage: Optional[ResponseUsage] = None,
        max_file_size_mb: Optional[float] = None,
        response_format: str = "json",
        temperature: float = 0,
        **kwargs,
    ) -> str:
        """
        Transcribe audio from a file path using Azure OpenAI.

        Args:
            audio_file_path: Path to the audio file
            language: Optional language code (e.g., 'ro', 'en')
            use_cache: Whether to use cached responses
            response_usage: Optional ResponseUsage object to track costs
            max_file_size_mb: Maximum allowed file size in megabytes
            response_format: Format of the response ('json', 'text', 'srt', 'verbose_json', 'vtt')
            temperature: Sampling temperature (0 to 1)
            **kwargs: Additional Azure-specific parameters

        Returns:
            Transcribed text as string

        Raises:
            AudioFileError: If there are issues with the audio file
            AudioTranscriptionError: If there are issues with the transcription API
        """
        try:
            result = await self.call_transcription_async(
                audio_input=audio_file_path,
                language=language,
                use_cache=use_cache,
                response_usage=response_usage,
                max_file_size_mb=max_file_size_mb,
                response_format=response_format,
                temperature=temperature,
                **kwargs,
            )
            return result.get("text", "")
        except Exception as e:
            if isinstance(e, (AudioFileError, AudioTranscriptionError)):
                raise
            raise AudioTranscriptionError(f"Azure file transcription error: {e}")

    async def transcribe_audio_segment(
        self,
        audio_segment: AudioSegment,
        language: Optional[str] = None,
        use_cache: bool = True,
        response_usage: Optional[ResponseUsage] = None,
        max_file_size_mb: Optional[float] = None,
        response_format: str = "json",
        temperature: float = 0,
        **kwargs,
    ) -> str:
        """
        Transcribe audio from an AudioSegment using Azure OpenAI.

        Args:
            audio_segment: AudioSegment object to transcribe
            language: Optional language code (e.g., 'ro', 'en')
            use_cache: Whether to use cached responses
            response_usage: Optional ResponseUsage object to track costs
            max_file_size_mb: Maximum allowed file size in megabytes
            response_format: Format of the response ('json', 'text', 'srt', 'verbose_json', 'vtt')
            temperature: Sampling temperature (0 to 1)
            **kwargs: Additional Azure-specific parameters

        Returns:
            Transcribed text as string

        Raises:
            AudioFileError: If there are issues with the AudioSegment
            AudioTranscriptionError: If there are issues with the transcription API
        """
        try:
            result = await self.call_transcription_async(
                audio_input=audio_segment,
                language=language,
                use_cache=use_cache,
                response_usage=response_usage,
                max_file_size_mb=max_file_size_mb,
                response_format=response_format,
                temperature=temperature,
                **kwargs,
            )
            return result.get("text", "")
        except Exception as e:
            if isinstance(e, (AudioFileError, AudioTranscriptionError)):
                raise
            raise AudioTranscriptionError(f"Azure segment transcription error: {e}")

    async def call_transcription_async(
        self,
        audio_input: Union[str, AudioSegment],
        language: Optional[str] = None,
        use_cache: bool = True,
        response_usage: Optional[ResponseUsage] = None,
        max_file_size_mb: Optional[float] = None,
        response_format: str = "json",
        temperature: float = 0,
        **kwargs,
    ) -> Dict:
        """
        Call Azure OpenAI transcription API and return full response data.

        Args:
            audio_input: Either file path (str) or AudioSegment object
            language: Optional language code (e.g., 'ro', 'en')
            use_cache: Whether to use cached responses
            response_usage: Optional ResponseUsage object to track costs
            max_file_size_mb: Maximum allowed file size in megabytes
            response_format: Format of the response ('json', 'text', 'srt', 'verbose_json', 'vtt')
            temperature: Sampling temperature (0 to 1)
            **kwargs: Additional Azure-specific parameters

        Returns:
            Dict containing full transcription response data

        Raises:
            AudioFileError: If there are issues with the audio input
            AudioTranscriptionError: If there are issues with the transcription API
        """
        try:
            # Set defaults

            max_file_size_mb = max_file_size_mb or self.get_max_file_size_mb()

            # Handle different input types
            if isinstance(audio_input, str):
                # File path input
                audio_file_path = audio_input
                file_content = self._validate_audio_file(audio_file_path)
                self._validate_file_size(audio_file_path, max_file_size_mb)

                # Generate cache key
                content_hash = hashlib.md5(file_content).hexdigest()

            else:
                # AudioSegment input
                audio_segment = audio_input

                # Create temporary file for AudioSegment
                with tempfile.NamedTemporaryFile(
                    suffix=".wav", delete=False
                ) as temp_file:
                    temp_path = temp_file.name

                try:
                    # Export AudioSegment to temporary file
                    audio_segment.export(temp_path, format="wav")
                    audio_file_path = temp_path
                    file_content = self._validate_audio_file(audio_file_path)
                    self._validate_file_size(audio_file_path, max_file_size_mb)

                    # Generate cache key
                    content_hash = hashlib.md5(file_content).hexdigest()

                except Exception as e:
                    # Clean up temp file on error
                    try:
                        if os.path.exists(temp_path):
                            os.remove(temp_path)
                    except:
                        pass
                    raise e

            # Setup cache
            cache_key = f"{content_hash}_{self.model_id}_{language or 'auto'}"
            cache_filename = f"{cache_key}.json"
            cache_filepath = os.path.join(LLM_AUDIO_CACHE_DIR, cache_filename)

            # Try to load from cache
            if use_cache:
                cached_response = self._load_cached_response(cache_filepath)
                if cached_response is not None:
                    return cached_response

            # Make transcription request using OpenAI library
            try:
                with open(audio_file_path, "rb") as audio_file:
                    # Prepare transcription parameters
                    transcription_params = {
                        "file": audio_file,
                        "model": self.model_id,
                        "response_format": response_format,
                        "temperature": temperature,
                    }

                    if language is not None:
                        transcription_params["language"] = language

                    # Call Azure OpenAI transcription
                    response = self.client.audio.transcriptions.create(
                        **transcription_params
                    )

                    # Convert response to dict format
                    if response_format == "json" or response_format == "verbose_json":
                        if hasattr(response, "text"):
                            response_data = {
                                "text": response.text,
                                "language": getattr(response, "language", None),
                            }
                        else:
                            # Handle string response
                            response_data = {"text": str(response)}
                    else:
                        # For other formats (text, srt, vtt), response is a string
                        response_data = {"text": str(response)}

            except Exception as e:
                raise AudioTranscriptionError(f"Azure OpenAI API error: {e}")

            finally:
                # Clean up temporary file if it was created for AudioSegment
                if isinstance(audio_input, AudioSegment):
                    try:
                        if os.path.exists(temp_path):
                            os.remove(temp_path)
                    except Exception as cleanup_error:
                        print(
                            f"Warning: Could not cleanup temp file {temp_path}: {cleanup_error}"
                        )

            # Calculate usage if requested
            if response_usage is not None:
                if isinstance(audio_input, str):
                    await self._calculate_usage_cost_from_file(
                        audio_file_path, response_data, response_usage
                    )
                else:
                    await self._calculate_usage_cost_from_segment(
                        audio_input, response_data, response_usage
                    )

            # Cache the response
            if use_cache:
                self._save_to_cache(cache_filepath, response_data)

            # Standardize the response format
            return self._standardize_response(response_data)

        except Exception as e:
            if isinstance(e, (AudioFileError, AudioTranscriptionError)):
                raise
            raise AudioTranscriptionError(f"Azure API call error: {e}")

    def _standardize_response(self, response_data: Dict) -> Dict:
        """
        Standardize response format across providers.

        Args:
            response_data: Raw response data from Azure

        Returns:
            Standardized response dictionary
        """
        # Ensure all responses have at least these fields
        standardized = {
            "text": response_data.get("text", ""),
            "language_code": response_data.get("language"),
            "provider": "azure",
        }

        # Preserve original response data
        standardized["raw_response"] = response_data

        return standardized

    async def _generate_transcription(
        self,
        audio_segment: AudioSegment,
        batch_size: int = 50,
        min_duration_seconds: float = 1 * 60,
        max_duration_seconds: float = 2 * 60,
        language: str = "ro",
        response_usage: Optional[ResponseUsage] = None,
    ) -> str:
        """
        Azure-specific transcription generation with silence-based splitting and size constraints.

        Args:
            audio_segment: AudioSegment to transcribe
            batch_size: Number of chunks to process in parallel
            min_duration_seconds: Minimum duration for merged chunks
            max_duration_seconds: Maximum duration for chunks
            language: Language code for transcription
            response_usage: Optional ResponseUsage object to track costs

        Returns:
            Transcribed text as string
        """
        # Split audio by silence first for better quality
        audio_segment_chunks = split_audio_segment_by_silence(
            audio_segment=audio_segment, max_duration_seconds=max_duration_seconds
        )
        print(
            f"Azure: Initial silence split created {len(audio_segment_chunks)} chunks"
        )

        # Merge chunks to fit within min/max duration and size constraints
        merged_chunks = merge_audio_chunks_by_duration(
            audio_segment_chunks,
            min_duration_seconds=min_duration_seconds,
            max_duration_seconds=max_duration_seconds,
        )
        print(f"Azure: After merging, {len(merged_chunks)} chunks remain")

        # Check size constraints and split further if needed
        final_chunks = []
        max_size_mb = self.get_max_file_size_mb()

        for chunk in merged_chunks:
            # Estimate chunk size (rough approximation)
            estimated_size_mb = (
                len(chunk) / 1000
            ) * 0.002  # Rough estimate: 2KB per second

            if estimated_size_mb > max_size_mb:
                # Split large chunks by duration to fit size limit
                chunk_duration = len(chunk) / 1000  # seconds
                target_duration = (
                    (max_size_mb / estimated_size_mb) * chunk_duration * 0.9
                )  # 90% safety margin

                # Split this chunk into smaller pieces
                chunk_start = 0
                while chunk_start < len(chunk):
                    chunk_end = min(
                        chunk_start + int(target_duration * 1000), len(chunk)
                    )
                    sub_chunk = chunk[chunk_start:chunk_end]
                    final_chunks.append(sub_chunk)
                    chunk_start = chunk_end
            else:
                final_chunks.append(chunk)

        print(f"Azure: After size constraint check, {len(final_chunks)} final chunks")

        # Process chunks in batches
        audio_segment_chunks_batches = [
            final_chunks[i : i + batch_size]
            for i in range(0, len(final_chunks), batch_size)
        ]

        result_transcriptions = []

        for batch in audio_segment_chunks_batches:
            tasks = [
                self.transcribe_audio_segment(
                    audio_segment=audio_segment_chunk,
                    language=language,
                    response_usage=response_usage,
                )
                for audio_segment_chunk in batch
            ]
            results = await asyncio.gather(*tasks)
            result_transcriptions.extend(results)

        result_transcript = " ".join(result_transcriptions)

        return result_transcript
